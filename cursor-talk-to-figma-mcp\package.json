{"name": "cursor-talk-to-figma-mcp", "description": "Cursor Talk to Figma MCP", "version": "0.3.1", "module": "dist/server.js", "main": "dist/server.js", "bin": {"cursor-talk-to-figma-mcp": "dist/server.js"}, "files": ["dist", "README.md"], "type": "module", "scripts": {"start": "bun run dist/server.js", "socket": "bun run src/socket.ts", "setup": "./scripts/setup.sh", "build": "tsup", "build:watch": "tsup --watch", "dev": "bun run build:watch", "pub:release": "bun run build && npm publish"}, "devDependencies": {"@types/bun": "latest", "bun-types": "^1.2.5", "tsup": "^8.4.0", "typescript": "^5.0.0"}, "dependencies": {"@modelcontextprotocol/sdk": "1.13.1", "uuid": "latest", "ws": "latest", "zod": "3.22.4"}}